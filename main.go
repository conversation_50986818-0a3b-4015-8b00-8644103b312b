package main

import (
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
)

// Python服务地址
const pythonServiceURL = "http://localhost:8008/inference_sft"

func main() {
	// 提供前端静态文件
	http.Handle("/", http.FileServer(http.Dir("./public")))

	// 音频流接口，供前端调用
	http.HandleFunc("/audio/stream", handleAudioStream)

	log.Println("Go server starting on :8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}

// 处理音频流请求
func handleAudioStream(w http.ResponseWriter, r *http.Request) {
	// 从请求中获取参数
	ttsText := r.URL.Query().Get("tts_text")
	spkId := r.URL.Query().Get("spk_id")

	log.Println("tts_text: %s, spk_id: %s", ttsText, spkId)
	if ttsText == "" || spkId == "" {
		http.Error(w, "missing parameters: tts_text and spk_id are required", http.StatusBadRequest)
		return
	}

	form := url.Values{}
	form.Set("tts_text", ttsText)
	form.Set("spk_id", spkId)
	formData := strings.NewReader(form.Encode())

	// 调用Python服务
	req, err := http.NewRequest("POST", pythonServiceURL, formData)
	if err != nil {
		http.Error(w, "failed to create request: "+err.Error(), http.StatusInternalServerError)
		return
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		http.Error(w, "failed to call python service: "+err.Error(), http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	// 设置响应头，告知前端这是流式音频
	w.Header().Set("Content-Type", "audio/x-pcm;rate=16000")
	w.Header().Set("Transfer-Encoding", "chunked")

	// 将Python服务返回的流直接转发给前端
	// 这是流式处理的关键，实现边接收边发送
	_, err = io.Copy(w, resp.Body)
	if err != nil && !strings.Contains(err.Error(), "write: broken pipe") {
		// 忽略客户端断开连接的错误
		log.Printf("error copying stream: %v", err)
	}
}

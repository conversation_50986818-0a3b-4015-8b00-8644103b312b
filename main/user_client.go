package main

import (
	"context"
	"log"
	"time"

	pb "your-module-path"  // 替换为你的实际模块路径
	"google.golang.org/grpc"
)

const (
	address = "localhost:50051"
)

func main() {
	// 连接到gRPC服务器
	conn, err := grpc.Dial(address, grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		log.Fatalf("无法连接到服务器: %v", err)
	}
	defer conn.Close()
	
	// 创建客户端
	c := pb.NewUserServiceClient(conn)

	// 创建用户
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	
	createResp, err := c.CreateUser(ctx, &pb.CreateUserRequest{
		Name:  "张三",
		Age:   30,
		Email: "<EMAIL>",
	})
	
	if err != nil {
		log.Fatalf("创建用户失败: %v", err)
	}
	
	log.Printf("创建用户成功，用户ID: %s", createResp.UserId)
	
	// 获取用户信息
	if createResp.Success {
		getCtx, getCancel := context.WithTimeout(context.Background(), time.Second)
		defer getCancel()
		
		getResp, err := c.GetUser(getCtx, &pb.GetUserRequest{UserId: createResp.UserId})
		if err != nil {
			log.Fatalf("获取用户信息失败: %v", err)
		}
		
		if getResp.Found {
			log.Printf("用户信息: ID=%s, 姓名=%s, 年龄=%d, 邮箱=%s",
				getResp.User.UserId, getResp.User.Name, getResp.User.Age, getResp.User.Email)
		} else {
			log.Printf("未找到用户，ID: %s", createResp.UserId)
		}
	}
}

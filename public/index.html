<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式音频播放器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <div class="container mx-auto px-4 py-8 max-w-2xl">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">流式音频合成</h1>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="mb-4">
                <label for="ttsText" class="block text-gray-700 mb-2">输入文本:</label>
                <textarea id="ttsText" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入要转换为语音的文本...">这是一个流式音频合成的示例。当您点击播放按钮后，系统会一边生成音频，一边进行播放，而不需要等待整个音频生成完成。</textarea>
            </div>
            
            <div class="mb-6">
                <label for="spkId" class="block text-gray-700 mb-2">选择发言人:</label>
                <select id="spkId" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="中文女">发言人 1</option>
                    <option value="1">发言人 2</option>
                    <option value="2">发言人 3</option>
                </select>
            </div>

            <div class="grid grid-cols-2 gap-4 mb-6">
                <div>
                    <label for="sampleRate" class="block text-gray-700 mb-2">采样率:</label>
                    <select id="sampleRate" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <option value="8000">8000 Hz</option>
                        <option value="16000" selected>16000 Hz</option>
                        <option value="22050">22050 Hz</option>
                        <option value="44100">44100 Hz</option>
                    </select>
                </div>
                <div>
                    <label for="bitDepth" class="block text-gray-700 mb-2">位深:</label>
                    <select id="bitDepth" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <option value="16" selected>16位</option>
                        <option value="8">8位</option>
                    </select>
                </div>
            </div>
            
            <div class="flex justify-center">
                <button id="playButton" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-6 rounded-full flex items-center transition duration-300">
                    <i class="fa fa-play mr-2"></i> 开始播放
                </button>
                <button id="stopButton" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded-full flex items-center ml-4 transition duration-300" disabled>
                    <i class="fa fa-stop mr-2"></i> 停止播放
                </button>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-4 mb-6">
            <h2 class="text-xl font-semibold mb-2 text-gray-700">状态信息</h2>
            <div id="status" class="text-gray-600 p-2 border border-gray-200 rounded-md min-h-[60px]">
                就绪，请输入文本并点击"开始播放"按钮
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-4">
            <h2 class="text-xl font-semibold mb-2 text-gray-700">调试信息</h2>
            <div id="debug" class="text-sm text-gray-600 p-2 border border-gray-200 rounded-md min-h-[100px] max-h-[200px] overflow-y-auto font-mono">
                等待调试信息...
            </div>
        </div>
    </div>

    <script>
        // 音频上下文，用于处理和播放音频
        let audioContext;
        // 用于处理流式数据的队列
        let audioQueue = [];
        // 是否正在播放
        let isPlaying = false;
        // 用于处理音频解码的worker
        let decoderWorker;
        // 当前正在播放的音频源
        let currentSource = null;
        // 音频配置 - 可通过UI调整
        let audioConfig = {
            sampleRate: 16000,  // 采样率，需与Python服务生成的一致
            bitDepth: 16,       // 位深，16位PCM
            channels: 1         // 声道数，单声道
        };
        // 保存不完整的字节（用于处理非2的倍数的情况）
        let remainingBytes = new Uint8Array(0);

        // DOM元素
        const playButton = document.getElementById('playButton');
        const stopButton = document.getElementById('stopButton');
        const statusElement = document.getElementById('status');
        const debugElement = document.getElementById('debug');
        const ttsTextInput = document.getElementById('ttsText');
        const spkIdSelect = document.getElementById('spkId');
        const sampleRateSelect = document.getElementById('sampleRate');
        const bitDepthSelect = document.getElementById('bitDepth');

        // 添加调试日志
        function logDebug(message) {
            const timestamp = new Date().toISOString().split('T')[1];
            debugElement.innerHTML += `[${timestamp}] ${message}\n`;
            debugElement.scrollTop = debugElement.scrollHeight;
        }

        // 更新状态信息
        function updateStatus(message) {
            statusElement.textContent = message;
            logDebug(message);
        }

        // 初始化解码器worker
        function initWorker() {
            if (decoderWorker) {
                decoderWorker.terminate();
            }

            decoderWorker = new Worker(URL.createObjectURL(new Blob([`
                // 将16位PCM转换为32位浮点数
                function pcm16ToFloat32(pcm16) {
                    const float32 = new Float32Array(pcm16.length);
                    for (let i = 0; i < pcm16.length; i++) {
                        // 16位PCM范围是-32768到32767，转换为-1.0到1.0
                        float32[i] = pcm16[i] / 32768;
                    }
                    return float32;
                }

                // 将8位PCM转换为32位浮点数
                function pcm8ToFloat32(pcm8) {
                    const float32 = new Float32Array(pcm8.length);
                    for (let i = 0; i < pcm8.length; i++) {
                        // 8位PCM范围是0到255，转换为-1.0到1.0
                        float32[i] = (pcm8[i] - 128) / 128;
                    }
                    return float32;
                }

                // 处理消息
                self.onmessage = function(e) {
                    if (e.data.type === 'decode') {
                        try {
                            let float32;
                            // 根据位深选择不同的解码方式
                            if (e.data.bitDepth === 16) {
                                // 将Uint8Array转换为Int16Array
                                const pcm16 = new Int16Array(e.data.data.buffer);
                                float32 = pcm16ToFloat32(pcm16);
                            } else {
                                // 8位PCM
                                const pcm8 = new Uint8Array(e.data.data.buffer);
                                float32 = pcm8ToFloat32(pcm8);
                            }
                            
                            // 发送解码后的数据回主线程
                            self.postMessage({
                                type: 'decoded',
                                data: float32,
                                length: float32.length
                            }, [float32.buffer]);
                        } catch (error) {
                            self.postMessage({
                                type: 'error',
                                message: error.message,
                                byteLength: e.data.data.byteLength
                            });
                        }
                    }
                };
            `], { type: 'application/javascript' })));

            // 接收解码后的音频数据
            decoderWorker.onmessage = function(e) {
                if (e.data.type === 'decoded') {
                    logDebug(`解码完成: ${e.data.length} 样本`);
                    audioQueue.push(e.data.data);
                    // 如果正在播放，尝试播放队列中的数据
                    if (isPlaying) {
                        processAudioQueue();
                    }
                } else if (e.data.type === 'error') {
                    logDebug(`解码错误: ${e.data.message}, 字节长度: ${e.data.byteLength}`);
                }
            };
        }

        // 处理音频队列
        function processAudioQueue() {
            if (audioQueue.length === 0 || !audioContext || audioContext.state !== 'running') {
                return;
            }

            // 停止当前正在播放的音频（如果有）
            if (currentSource) {
                currentSource.stop();
            }

            // 创建音频缓冲区
            const buffer = audioContext.createBuffer(
                audioConfig.channels,
                audioQueue[0].length,
                audioConfig.sampleRate
            );
            
            // 将数据复制到缓冲区
            buffer.copyToChannel(audioQueue[0], 0);
            audioQueue.shift(); // 移除已处理的数据
            
            logDebug(`播放音频片段: ${buffer.length} 样本, 持续时间: ${(buffer.duration * 1000).toFixed(1)}ms`);
            
            // 创建新的音频源并连接到输出
            currentSource = audioContext.createBufferSource();
            currentSource.buffer = buffer;
            currentSource.connect(audioContext.destination);
            
            // 播放结束后继续处理队列
            currentSource.onended = function() {
                logDebug("音频片段播放结束");
                currentSource = null;
                if (isPlaying) {
                    processAudioQueue();
                }
            };
            
            // 开始播放
            currentSource.start(0);
        }

        // 处理接收到的原始数据，确保16位PCM的字节数是2的倍数
        function processRawData(rawData) {
            // 合并之前剩余的字节和新接收的字节
            const combinedLength = remainingBytes.length + rawData.length;
            const combinedData = new Uint8Array(combinedLength);
            
            // 复制剩余字节
            combinedData.set(remainingBytes, 0);
            // 复制新接收的字节
            combinedData.set(rawData, remainingBytes.length);
            
            // 重置剩余字节
            remainingBytes = new Uint8Array(0);
            
            // 对于16位PCM，需要确保字节数是2的倍数
            if (audioConfig.bitDepth === 16) {
                const remainder = combinedData.length % 2;
                if (remainder !== 0) {
                    // 保存多余的字节供下次处理
                    remainingBytes = combinedData.slice(combinedData.length - remainder);
                    // 截断数据，确保是2的倍数
                    const validData = combinedData.slice(0, combinedData.length - remainder);
                    logDebug(`调整字节长度: 原始=${combinedData.length}, 有效=${validData.length}, 剩余=${remainingBytes.length}`);
                    return validData;
                }
            }
            
            return combinedData;
        }

        // 开始流式播放
        async function startStreaming() {
            const ttsText = ttsTextInput.value.trim();
            const spkId = spkIdSelect.value;
            
            if (!ttsText) {
                updateStatus("请输入文本内容");
                return;
            }
            
            // 重置剩余字节缓存
            remainingBytes = new Uint8Array(0);
            
            // 更新音频配置
            audioConfig.sampleRate = parseInt(sampleRateSelect.value);
            audioConfig.bitDepth = parseInt(bitDepthSelect.value);
            logDebug(`音频配置: 采样率=${audioConfig.sampleRate}Hz, 位深=${audioConfig.bitDepth}位`);
            
            // 重置状态
            stopStreaming();
            updateStatus("正在连接服务器...");
            
            try {
                // 初始化音频上下文（确保在用户交互中创建）
                if (!audioContext) {
                    logDebug("创建新的AudioContext");
                    audioContext = new (window.AudioContext || window.webkitAudioContext)({
                        sampleRate: audioConfig.sampleRate
                    });
                } else if (audioContext.state === 'suspended') {
                    logDebug("恢复AudioContext");
                    await audioContext.resume();
                }
                
                logDebug(`AudioContext状态: ${audioContext.state}`);
                if (audioContext.state !== 'running') {
                    throw new Error("音频上下文未能正常启动，请重试");
                }
                
                // 更新UI状态
                isPlaying = true;
                playButton.disabled = true;
                stopButton.disabled = false;
                updateStatus("正在接收并播放音频...");
                
                // 构建请求URL
                const url = `/audio/stream?tts_text=${encodeURIComponent(ttsText)}&spk_id=${encodeURIComponent(spkId)}`;
                logDebug(`请求URL: ${url}`);
                
                // 发起请求获取音频流
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                }
                
                if (!response.body) {
                    throw new Error("服务器未返回流式数据");
                }
                
                // 获取可读流
                const reader = response.body.getReader();
                let totalBytes = 0;
                
                // 处理流的函数
                const processStream = async () => {
                    // 读取流数据
                    const { done, value } = await reader.read();
                    
                    // 如果流结束
                    if (done) {
                        // 处理可能剩余的字节
                        if (remainingBytes.length > 0) {
                            logDebug(`处理剩余字节: ${remainingBytes.length} 字节`);
                            // 对于16位PCM，如果最后还有1个字节，直接丢弃
                            if (audioConfig.bitDepth === 16 && remainingBytes.length === 1) {
                                logDebug(`丢弃最后1个字节`);
                            } else {
                                // 发送剩余字节进行解码
                                decoderWorker.postMessage({
                                    type: 'decode',
                                    data: remainingBytes,
                                    bitDepth: audioConfig.bitDepth
                                }, [remainingBytes.buffer]);
                            }
                            remainingBytes = new Uint8Array(0);
                        }
                        
                        logDebug(`流处理完成，总接收字节: ${totalBytes}`);
                        updateStatus("音频传输完成，等待播放结束...");
                        // 等待所有音频播放完成
                        const checkQueue = setInterval(() => {
                            if (audioQueue.length === 0 && !currentSource) {
                                clearInterval(checkQueue);
                                updateStatus("音频播放完成");
                                stopStreaming();
                            }
                        }, 100);
                        return;
                    }
                    
                    // 累计接收字节数
                    totalBytes += value.length;
                    logDebug(`接收数据: ${value.length} 字节, 累计: ${totalBytes} 字节`);
                    
                    // 处理原始数据，确保格式正确
                    const processedData = processRawData(value);
                    
                    if (processedData.length > 0) {
                        // 将处理后的二进制数据发送给worker解码
                        decoderWorker.postMessage({
                            type: 'decode',
                            data: processedData,
                            bitDepth: audioConfig.bitDepth
                        }, [processedData.buffer]);
                    }
                    
                    // 继续处理流
                    processStream();
                };
                
                // 开始处理流
                processStream();
                
            } catch (error) {
                console.error("播放错误:", error);
                updateStatus(`播放失败: ${error.message}`);
                stopStreaming();
            }
        }

        // 停止播放
        function stopStreaming() {
            isPlaying = false;
            if (currentSource) {
                currentSource.stop();
                currentSource = null;
            }
            audioQueue = [];
            remainingBytes = new Uint8Array(0);
            
            // 更新UI状态
            playButton.disabled = false;
            stopButton.disabled = true;
            logDebug("播放已停止");
        }

        // 事件监听
        playButton.addEventListener('click', startStreaming);
        stopButton.addEventListener('click', stopStreaming);
        
        // 监听音频配置变化
        sampleRateSelect.addEventListener('change', () => {
            logDebug(`采样率已变更为: ${sampleRateSelect.value}Hz`);
        });
        
        bitDepthSelect.addEventListener('change', () => {
            logDebug(`位深已变更为: ${bitDepthSelect.value}位`);
        });

        // 初始化
        window.addEventListener('load', () => {
            initWorker();
            logDebug("页面加载完成，等待用户操作...");
            
            // 为了确保音频上下文能正常启动，在用户首次交互时做准备
            document.body.addEventListener('click', () => {
                if (!audioContext) {
                    logDebug("用户交互检测到，准备创建AudioContext");
                    // 预创建音频上下文（但不启动）
                    try {
                        audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        logDebug("AudioContext已预创建");
                    } catch (error) {
                        logDebug(`预创建AudioContext失败: ${error.message}`);
                    }
                }
            }, { once: true });
        });

        // 页面关闭时清理资源
        window.addEventListener('beforeunload', () => {
            if (decoderWorker) {
                decoderWorker.terminate();
            }
            if (audioContext) {
                audioContext.close();
            }
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS 语音合成服务</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
            font-size: 1.1em;
        }

        textarea, select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }

        textarea {
            resize: vertical;
            min-height: 120px;
        }

        textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }

        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        button {
            flex: 1;
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .audio-controls {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            text-align: center;
        }

        audio {
            width: 100%;
            margin-top: 15px;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-weight: 500;
        }

        .status.loading {
            background: #e3f2fd;
            color: #1976d2;
            border: 2px solid #bbdefb;
        }

        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #c8e6c9;
        }

        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 2px solid #ffcdd2;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1976d2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 TTS 语音合成</h1>
        
        <form id="ttsForm">
            <div class="form-group">
                <label for="ttsText">输入文本：</label>
                <textarea 
                    id="ttsText" 
                    name="tts_text" 
                    placeholder="请输入要转换为语音的文本..."
                    required
                ></textarea>
            </div>

            <div class="form-group">
                <label for="spkId">说话人ID：</label>
                <select id="spkId" name="spk_id" required>
                    <option value="">请选择说话人</option>
                    <option value="0">说话人 0</option>
                    <option value="1">说话人 1</option>
                    <option value="2">说话人 2</option>
                    <option value="3">说话人 3</option>
                    <option value="4">说话人 4</option>
                </select>
            </div>

            <div class="button-group">
                <button type="submit" class="btn-primary" id="generateBtn">
                    🎵 生成语音
                </button>
                <button type="button" class="btn-secondary" id="clearBtn">
                    🗑️ 清空
                </button>
            </div>
        </form>

        <div id="status" class="status hidden"></div>

        <div id="audioControls" class="audio-controls hidden">
            <h3>🎧 音频播放</h3>
            <audio id="audioPlayer" controls></audio>
            <div class="button-group" style="margin-top: 15px;">
                <button type="button" class="btn-secondary" id="downloadBtn">
                    💾 下载音频
                </button>
                <button type="button" class="btn-danger" id="stopBtn">
                    ⏹️ 停止播放
                </button>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('ttsForm');
        const generateBtn = document.getElementById('generateBtn');
        const clearBtn = document.getElementById('clearBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('status');
        const audioControls = document.getElementById('audioControls');
        const audioPlayer = document.getElementById('audioPlayer');
        const ttsText = document.getElementById('ttsText');
        const spkId = document.getElementById('spkId');

        let currentAudioBlob = null;

        // 显示状态信息
        function showStatus(message, type = 'loading') {
            status.className = `status ${type}`;
            status.innerHTML = type === 'loading' 
                ? `<span class="loading-spinner"></span>${message}`
                : message;
            status.classList.remove('hidden');
        }

        // 隐藏状态信息
        function hideStatus() {
            status.classList.add('hidden');
        }

        // 表单提交处理
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const text = ttsText.value.trim();
            const speaker = spkId.value;

            if (!text || !speaker) {
                showStatus('请填写完整的文本和说话人信息', 'error');
                return;
            }

            try {
                generateBtn.disabled = true;
                showStatus('正在生成语音，请稍候...', 'loading');
                audioControls.classList.add('hidden');

                // 构建请求URL
                const params = new URLSearchParams({
                    tts_text: text,
                    spk_id: speaker
                });

                const response = await fetch(`/audio/stream?${params}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'audio/x-pcm'
                    }
                });

                if (!response.ok) {
                    throw new Error(`服务器错误: ${response.status} ${response.statusText}`);
                }

                // 读取音频流数据
                const audioData = await response.arrayBuffer();
                currentAudioBlob = new Blob([audioData], { type: 'audio/wav' });
                
                // 创建音频URL并播放
                const audioUrl = URL.createObjectURL(currentAudioBlob);
                audioPlayer.src = audioUrl;
                
                showStatus('语音生成成功！', 'success');
                audioControls.classList.remove('hidden');
                
                // 自动播放
                audioPlayer.play().catch(err => {
                    console.log('自动播放失败，需要用户交互:', err);
                });

            } catch (error) {
                console.error('生成语音失败:', error);
                showStatus(`生成失败: ${error.message}`, 'error');
            } finally {
                generateBtn.disabled = false;
            }
        });

        // 清空按钮
        clearBtn.addEventListener('click', () => {
            ttsText.value = '';
            spkId.value = '';
            hideStatus();
            audioControls.classList.add('hidden');
            if (audioPlayer.src) {
                URL.revokeObjectURL(audioPlayer.src);
                audioPlayer.src = '';
            }
            currentAudioBlob = null;
        });

        // 下载按钮
        downloadBtn.addEventListener('click', () => {
            if (currentAudioBlob) {
                const url = URL.createObjectURL(currentAudioBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `tts_${Date.now()}.wav`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        });

        // 停止播放按钮
        stopBtn.addEventListener('click', () => {
            audioPlayer.pause();
            audioPlayer.currentTime = 0;
        });

        // 音频播放事件监听
        audioPlayer.addEventListener('loadstart', () => {
            console.log('开始加载音频');
        });

        audioPlayer.addEventListener('canplay', () => {
            console.log('音频可以播放');
        });

        audioPlayer.addEventListener('error', (e) => {
            console.error('音频播放错误:', e);
            showStatus('音频播放失败', 'error');
        });
    </script>
</body>
</html>

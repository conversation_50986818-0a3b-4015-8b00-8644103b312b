syntax = "proto3";

package user;


option go_package = "./;user";

// 用户服务定义
service UserService {
  // 获取用户信息
  rpc GetUser (GetUserRequest) returns (UserResponse);
  
  // 创建用户
  rpc CreateUser (CreateUserRequest) returns (CreateUserResponse);
}

// 获取用户请求
message GetUserRequest {
  string user_id = 1;
}

// 用户信息
message User {
  string user_id = 1;
  string name = 2;
  int32 age = 3;
  string email = 4;
}

// 获取用户响应
message UserResponse {
  User user = 1;
  bool found = 2;
}

// 创建用户请求
message CreateUserRequest {
  string name = 1;
  int32 age = 2;
  string email = 3;
}

// 创建用户响应
message CreateUserResponse {
  string user_id = 1;
  bool success = 2;
}
